# === Advanced Forex AI Trader using LangChain 0.3.27 + Gemini + P&L Tracking ===

import MetaTrader5 as mt5
import time
import os
import logging
from dotenv import load_dotenv
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.prompts import PromptTemplate

load_dotenv()

# === Load credentials ===
MT5_LOGIN = int(os.getenv("MT5_LOGIN"))
MT5_PASSWORD = os.getenv("MT5_PASSWORD")
MT5_SERVER = os.getenv("MT5_SERVER")
GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY")

# === Constants ===
SYMBOL = "EURUSD"
LOT_SIZE = 0.1
BAR_COUNT = 20
TIMEFRAME = mt5.TIMEFRAME_M1
DELAY_SECONDS = 60
MAGIC_ID = 123456

# Track last trade
last_trade = {
    "action": None,
    "price": None
}

# === Logger ===
logging.basicConfig(level=logging.INFO, format="[%(asctime)s] %(message)s")

# === Init MT5 ===
def init_mt5():
    if not mt5.initialize():
        logging.error("MT5 initialize failed: %s", mt5.last_error())
        exit()
    if not mt5.login(MT5_LOGIN, password=MT5_PASSWORD, server=MT5_SERVER):
        logging.error("Login failed: %s", mt5.last_error())
        exit()
    logging.info("Connected to MetaTrader5.")

# === Fetch rates ===
def get_forex_data(symbol=SYMBOL, bars=BAR_COUNT):
    rates = mt5.copy_rates_from_pos(symbol, TIMEFRAME, 0, bars)
    if not rates or len(rates) < 5:
        raise ValueError("Insufficient data")
    return rates

# === Trade Execution ===
def place_trade(action, symbol=SYMBOL, lot=LOT_SIZE):
    global last_trade
    tick = mt5.symbol_info_tick(symbol)
    if not tick:
        raise Exception("Tick data unavailable")

    price = tick.ask if action == "BUY" else tick.bid
    order_type = mt5.ORDER_TYPE_BUY if action == "BUY" else mt5.ORDER_TYPE_SELL

    request = {
        "action": mt5.TRADE_ACTION_DEAL,
        "symbol": symbol,
        "volume": lot,
        "type": order_type,
        "price": price,
        "deviation": 20,
        "magic": MAGIC_ID,
        "comment": "LangChain Gemini Order",
        "type_time": mt5.ORDER_TIME_GTC,
        "type_filling": mt5.ORDER_FILLING_IOC,
    }

    result = mt5.order_send(request)
    if result.retcode == mt5.TRADE_RETCODE_DONE:
        last_trade["action"] = action
        last_trade["price"] = price
        logging.info(f"{action} trade placed at {price}")
    else:
        logging.warning("Trade failed: %s", result)
    return result

# === Gemini LLM ===
def get_gemini_llm():
    return ChatGoogleGenerativeAI(
        model="gemini-1.5-flash",
        temperature=0.1,
        google_api_key=GOOGLE_API_KEY,
    )

# === Prompt + Decision ===
def generate_decision(llm, rates):
    closes = [round(r['close'], 5) for r in rates]
    highs = [round(r['high'], 5) for r in rates]
    lows = [round(r['low'], 5) for r in rates]
    volumes = [int(r['tick_volume']) for r in rates]
    current_price = closes[-1]

    prompt = PromptTemplate.from_template("""
You are a professional Forex trader AI.
Analyze:
- Current price: {price}
- Closing prices: {closes}
- Highs: {highs}
- Lows: {lows}
- Tick volumes: {volumes}

Decide: BUY (if uptrend), SELL (if downtrend), or HOLD (if unclear). One word only.
""").format(
        price=current_price,
        closes=closes[-5:],
        highs=highs[-5:],
        lows=lows[-5:],
        volumes=volumes[-5:]
    )

    response = llm.invoke(prompt).strip().upper()
    logging.info("AI Decision: %s", response)
    return response

# === P/L % Calculation ===
def calculate_profit_loss():
    global last_trade
    if not last_trade["action"] or not last_trade["price"]:
        return "No open trade to track."

    current_tick = mt5.symbol_info_tick(SYMBOL)
    if not current_tick:
        return "Tick unavailable"

    current_price = current_tick.bid if last_trade["action"] == "SELL" else current_tick.ask
    entry_price = last_trade["price"]
    direction = 1 if last_trade["action"] == "BUY" else -1
    change_percent = ((current_price - entry_price) / entry_price) * 100 * direction

    return f"{change_percent:+.2f}% {'Profit' if change_percent > 0 else 'Loss'}"

# === Run the AI Loop ===
def run_forex_ai():
    init_mt5()
    llm = get_gemini_llm()

    while True:
        try:
            rates = get_forex_data()
            decision = generate_decision(llm, rates)

            if decision in ["BUY", "SELL"]:
                place_trade(decision)
            else:
                logging.info("Decision: HOLD — no trade placed.")

            pl_result = calculate_profit_loss()
            logging.info("P&L update: %s", pl_result)

        except Exception as e:
            logging.error("Error: %s", e)

        logging.info("Next check in %d seconds...\n", DELAY_SECONDS)
        time.sleep(DELAY_SECONDS)

# === Run the Bot ===
if __name__ == "__main__":
    run_forex_ai()
