#!/usr/bin/env python3
"""
Test script for enhanced bot_v1.py features
- Enhanced error reporting
- External market analysis with Tavily search
"""

import sys
import os
from dotenv import load_dotenv
import MetaTrader5 as mt5

# Add current directory to path
sys.path.append('.')
from bot_v1 import (
    init_mt5, 
    get_gemini_llm, 
    get_forex_data, 
    generate_decision,
    get_external_market_analysis,
    get_trade_error_reason,
    place_trade
)

load_dotenv()

def test_enhanced_features():
    """Test all enhanced features"""
    print("🚀 Testing Enhanced Bot Features")
    print("=" * 50)
    
    # Initialize MT5
    try:
        init_mt5()
        print("✅ MT5 Connection: SUCCESS")
    except Exception as e:
        print(f"❌ MT5 Connection: FAILED - {e}")
        return
    
    # Test Gemini LLM
    try:
        llm = get_gemini_llm()
        print("✅ Gemini LLM: SUCCESS")
    except Exception as e:
        print(f"❌ Gemini LLM: FAILED - {e}")
        return
    
    # Test forex data
    try:
        rates = get_forex_data()
        print(f"✅ Forex Data: SUCCESS - {len(rates)} bars retrieved")
    except Exception as e:
        print(f"❌ Forex Data: FAILED - {e}")
        return
    
    # Test external market analysis (if Tavily API key is set)
    tavily_key = os.getenv("TAVILY_API_KEY")
    if tavily_key and tavily_key != "tvly-YOUR_TAVILY_API_KEY_HERE":
        try:
            print("\n🔍 Testing External Market Analysis...")
            analysis = get_external_market_analysis("EURUSD")
            print("✅ External Analysis: SUCCESS")
            print(f"📰 Sample Analysis: {analysis[:200]}...")
        except Exception as e:
            print(f"❌ External Analysis: FAILED - {e}")
    else:
        print("⚠️  External Analysis: SKIPPED (No valid Tavily API key)")
    
    # Test enhanced decision making
    try:
        print("\n🤖 Testing Enhanced AI Decision Making...")
        # Test without external analysis first
        decision1 = generate_decision(llm, rates, include_external=False)
        print(f"✅ Technical Analysis Decision: {decision1}")
        
        # Test with external analysis (if available)
        if tavily_key and tavily_key != "tvly-YOUR_TAVILY_API_KEY_HERE":
            decision2 = generate_decision(llm, rates, include_external=True)
            print(f"✅ Combined Analysis Decision: {decision2}")
        
    except Exception as e:
        print(f"❌ Enhanced Decision Making: FAILED - {e}")
    
    # Test error reporting
    print("\n🔧 Testing Enhanced Error Reporting...")
    error_codes = [
        mt5.TRADE_RETCODE_MARKET_CLOSED,
        mt5.TRADE_RETCODE_NO_MONEY,
        mt5.TRADE_RETCODE_INVALID_PRICE,
        mt5.TRADE_RETCODE_REQUOTE
    ]
    
    for code in error_codes:
        try:
            error_msg = get_trade_error_reason(code)
            print(f"✅ Error Code {code}: {error_msg}")
        except Exception as e:
            print(f"❌ Error Code {code}: FAILED - {e}")
    
    # Cleanup
    mt5.shutdown()
    print("\n🎉 Enhanced Bot Testing Complete!")
    print("=" * 50)

if __name__ == "__main__":
    test_enhanced_features()
