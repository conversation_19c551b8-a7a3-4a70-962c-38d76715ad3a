#!/usr/bin/env python3
"""
Test MT5 connection and diagnose issues
"""

import MetaTrader5 as mt5
import os
from dotenv import load_dotenv

load_dotenv()

def test_mt5_connection():
    print("🔧 MetaTrader5 Connection Diagnostic")
    print("=" * 50)
    
    # Check environment variables
    login = os.getenv("MT5_LOGIN")
    password = os.getenv("MT5_PASSWORD")
    server = os.getenv("MT5_SERVER")
    
    print(f"📋 Credentials Check:")
    print(f"   Login: {login}")
    print(f"   Server: {server}")
    print(f"   Password: {'*' * len(password) if password else 'NOT SET'}")
    print()
    
    # Test MT5 initialization
    print("🚀 Testing MT5 initialization...")
    if not mt5.initialize():
        error = mt5.last_error()
        print(f"❌ MT5 initialize failed: {error}")
        print("\n🔍 Possible solutions:")
        print("1. Make sure MetaTrader5 terminal is installed and running")
        print("2. Check if MT5 terminal is logged in manually first")
        print("3. Restart MT5 terminal")
        print("4. Run as administrator")
        return False
    else:
        print("✅ MT5 initialized successfully")
    
    # Test login
    print("\n🔐 Testing login...")
    try:
        login_int = int(login)
        if not mt5.login(login_int, password=password, server=server):
            error = mt5.last_error()
            print(f"❌ Login failed: {error}")
            
            print("\n🔍 Troubleshooting steps:")
            print("1. Check if credentials are correct")
            print("2. Verify the demo account is still active")
            print("3. Try logging in manually through MT5 terminal first")
            print("4. Check internet connection")
            print("5. Contact broker if account is suspended")
            
            # Try to get more info
            print(f"\n📊 Connection attempt details:")
            print(f"   Login: {login_int}")
            print(f"   Server: {server}")
            print(f"   Error code: {error[0] if isinstance(error, tuple) else error}")
            
            mt5.shutdown()
            return False
        else:
            print("✅ Login successful!")
            
            # Get account info
            account_info = mt5.account_info()
            if account_info:
                print(f"\n💰 Account Information:")
                print(f"   Account: {account_info.login}")
                print(f"   Name: {account_info.name}")
                print(f"   Server: {account_info.server}")
                print(f"   Currency: {account_info.currency}")
                print(f"   Balance: ${account_info.balance:.2f}")
                print(f"   Equity: ${account_info.equity:.2f}")
                print(f"   Margin: ${account_info.margin:.2f}")
                print(f"   Free Margin: ${account_info.margin_free:.2f}")
                print(f"   Leverage: 1:{account_info.leverage}")
                print(f"   Trade Allowed: {account_info.trade_allowed}")
                print(f"   Trade Expert: {account_info.trade_expert}")
            
            mt5.shutdown()
            return True
            
    except ValueError as e:
        print(f"❌ Invalid login format: {e}")
        print("Login must be a number")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    success = test_mt5_connection()
    if success:
        print("\n🎉 MT5 connection test PASSED!")
    else:
        print("\n❌ MT5 connection test FAILED!")
