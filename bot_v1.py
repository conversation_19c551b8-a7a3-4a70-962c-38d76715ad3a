# === Advanced Forex AI Trader using LangChain 0.3.27 + Gemini + P&L Tracking + Tavily Search ===

import MetaTrader5 as mt5
import time
import os
import logging
from dotenv import load_dotenv
from langchain_google_genai import ChatGoogleGenerativeAI

# from langchain_core.prompts import PromptTemplate  # Not needed anymore
from langchain_community.tools.tavily_search import TavilySearchResults

load_dotenv()

# === Load credentials ===
MT5_LOGIN = int(os.getenv("MT5_LOGIN"))
MT5_PASSWORD = os.getenv("MT5_PASSWORD")
MT5_SERVER = os.getenv("MT5_SERVER")
GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY")
TAVILY_API_KEY = os.getenv("TAVILY_API_KEY")

# === Constants ===
SYMBOL = "EURUSD"
LOT_SIZE = 0.1
BAR_COUNT = 20
TIMEFRAME = mt5.TIMEFRAME_M1
DELAY_SECONDS = 60
MAGIC_ID = 123456

# Track last trade
last_trade = {"action": None, "price": None}

# === Logger ===
logging.basicConfig(level=logging.INFO, format="[%(asctime)s] %(message)s")


# === Init MT5 ===
def init_mt5():
    if not mt5.initialize():
        logging.error("MT5 initialize failed: %s", mt5.last_error())
        exit()
    if not mt5.login(MT5_LOGIN, password=MT5_PASSWORD, server=MT5_SERVER):
        logging.error("Login failed: %s", mt5.last_error())
        exit()
    logging.info("Connected to MetaTrader5.")


# === Fetch rates ===
def get_forex_data(symbol=SYMBOL, bars=BAR_COUNT):
    rates = mt5.copy_rates_from_pos(symbol, TIMEFRAME, 0, bars)
    if rates is None or len(rates) < 5:
        raise ValueError("Insufficient data")
    return rates


# === Trade Execution with Enhanced Error Reporting ===
def place_trade(action, symbol=SYMBOL, lot=LOT_SIZE):
    global last_trade
    tick = mt5.symbol_info_tick(symbol)
    if not tick:
        error_msg = "Tick data unavailable - Market may be closed or symbol not found"
        logging.error(error_msg)
        raise Exception(error_msg)

    price = tick.ask if action == "BUY" else tick.bid
    order_type = mt5.ORDER_TYPE_BUY if action == "BUY" else mt5.ORDER_TYPE_SELL

    request = {
        "action": mt5.TRADE_ACTION_DEAL,
        "symbol": symbol,
        "volume": lot,
        "type": order_type,
        "price": price,
        "deviation": 20,
        "magic": MAGIC_ID,
        "comment": "LangChain Gemini Order",
        "type_time": mt5.ORDER_TIME_GTC,
        "type_filling": mt5.ORDER_FILLING_IOC,
    }

    result = mt5.order_send(request)
    if result.retcode == mt5.TRADE_RETCODE_DONE:
        last_trade["action"] = action
        last_trade["price"] = price
        logging.info(f"✅ {action} trade placed successfully at {price}")
    else:
        # Enhanced error reporting
        error_reason = get_trade_error_reason(result.retcode)
        logging.error(f"❌ {action} trade FAILED: {error_reason}")
        logging.error(f"   Error Code: {result.retcode}")
        logging.error(f"   Error Details: {result}")

        # Log market conditions when trade fails
        log_market_conditions(symbol, action, price)

    return result


# === Enhanced Error Reporting ===
def get_trade_error_reason(retcode):
    """Convert MT5 error codes to human-readable reasons"""
    error_codes = {
        mt5.TRADE_RETCODE_REQUOTE: "Price changed (requote) - Market moving too fast",
        mt5.TRADE_RETCODE_REJECT: "Request rejected by broker - Check account status",
        mt5.TRADE_RETCODE_CANCEL: "Request canceled by trader",
        mt5.TRADE_RETCODE_PLACED: "Order placed successfully",
        mt5.TRADE_RETCODE_DONE_PARTIAL: "Request completed partially",
        mt5.TRADE_RETCODE_ERROR: "Common error - Check connection",
        mt5.TRADE_RETCODE_TIMEOUT: "Request timeout - Server busy",
        mt5.TRADE_RETCODE_INVALID: "Invalid request parameters",
        mt5.TRADE_RETCODE_INVALID_VOLUME: "Invalid volume - Check lot size",
        mt5.TRADE_RETCODE_INVALID_PRICE: "Invalid price - Market may be closed",
        mt5.TRADE_RETCODE_INVALID_STOPS: "Invalid stop levels",
        mt5.TRADE_RETCODE_TRADE_DISABLED: "Trading disabled for this symbol",
        mt5.TRADE_RETCODE_MARKET_CLOSED: "Market is closed",
        mt5.TRADE_RETCODE_NO_MONEY: "Insufficient funds",
        mt5.TRADE_RETCODE_PRICE_CHANGED: "Price changed during execution",
        mt5.TRADE_RETCODE_PRICE_OFF: "Invalid price - Outside allowed range",
        mt5.TRADE_RETCODE_INVALID_EXPIRATION: "Invalid expiration time",
        mt5.TRADE_RETCODE_ORDER_CHANGED: "Order state changed",
        mt5.TRADE_RETCODE_TOO_MANY_REQUESTS: "Too many requests - Rate limited",
        mt5.TRADE_RETCODE_NO_CHANGES: "No changes in request",
        mt5.TRADE_RETCODE_SERVER_DISABLES_AT: "Auto trading disabled by server",
        mt5.TRADE_RETCODE_CLIENT_DISABLES_AT: "Auto trading disabled by client",
        mt5.TRADE_RETCODE_LOCKED: "Request locked for processing",
        mt5.TRADE_RETCODE_FROZEN: "Order or position frozen",
        mt5.TRADE_RETCODE_INVALID_FILL: "Invalid order filling type",
        mt5.TRADE_RETCODE_CONNECTION: "No connection to trade server",
        mt5.TRADE_RETCODE_ONLY_REAL: "Operation allowed only for live accounts",
        mt5.TRADE_RETCODE_LIMIT_ORDERS: "Limit on number of orders reached",
        mt5.TRADE_RETCODE_LIMIT_VOLUME: "Volume limit reached",
    }
    return error_codes.get(retcode, f"Unknown error code: {retcode}")


def log_market_conditions(symbol, action, price):
    """Log current market conditions when trade fails"""
    try:
        symbol_info = mt5.symbol_info(symbol)
        if symbol_info:
            logging.info(f"📊 Market Conditions for {symbol}:")
            logging.info(f"   Spread: {symbol_info.spread} points")
            logging.info(f"   Ask: {symbol_info.ask}, Bid: {symbol_info.bid}")
            logging.info(f"   Trading allowed: {symbol_info.trade_mode}")
            logging.info(
                f"   Market session: {'Open' if symbol_info.trade_mode > 0 else 'Closed'}"
            )

        # Check account info
        account_info = mt5.account_info()
        if account_info:
            logging.info(f"💰 Account Status:")
            logging.info(f"   Balance: ${account_info.balance:.2f}")
            logging.info(f"   Free Margin: ${account_info.margin_free:.2f}")
            logging.info(f"   Margin Level: {account_info.margin_level:.2f}%")

    except Exception as e:
        logging.error(f"Failed to log market conditions: {e}")


# === Tavily Search for External Market Analysis ===
def get_tavily_search():
    """Initialize Tavily search tool"""
    return TavilySearchResults(
        max_results=3, search_depth="advanced", api_key=TAVILY_API_KEY
    )


def get_external_market_analysis(symbol="EURUSD"):
    """Get external market analysis using Tavily search"""
    try:
        search_tool = get_tavily_search()

        # Search for recent market analysis
        search_queries = [
            f"{symbol} forex analysis today market outlook",
            f"EUR USD technical analysis forecast",
            f"forex market news {symbol} trading signals",
        ]

        external_insights = []
        for query in search_queries:
            try:
                results = search_tool.run(query)
                if results:
                    external_insights.extend(
                        results[:1]
                    )  # Take top result from each query
            except Exception as e:
                logging.warning(f"Search query failed: {query} - {e}")
                continue

        if external_insights:
            # Summarize findings
            summary = "\n".join(
                [
                    f"• {result.get('title', 'N/A')}: {result.get('content', 'N/A')[:200]}..."
                    for result in external_insights[:3]
                ]
            )
            return summary
        else:
            return "No external market analysis available"

    except Exception as e:
        logging.error(f"External market analysis failed: {e}")
        return "External analysis unavailable"


# === Gemini LLM ===
def get_gemini_llm():
    return ChatGoogleGenerativeAI(
        model="gemini-2.5-flash",
        temperature=0.1,
        google_api_key=GOOGLE_API_KEY,
    )


# === Enhanced Prompt + Decision with External Analysis ===
def generate_decision(llm: ChatGoogleGenerativeAI, rates, include_external=True):
    closes = [round(r["close"], 5) for r in rates]
    highs = [round(r["high"], 5) for r in rates]
    lows = [round(r["low"], 5) for r in rates]
    volumes = [int(r["tick_volume"]) for r in rates]
    current_price = closes[-1]

    # Get external market analysis
    external_analysis = ""
    if include_external:
        try:
            logging.info("🔍 Fetching external market analysis...")
            external_analysis = get_external_market_analysis(SYMBOL)
            logging.info("📰 External Analysis Retrieved")
        except Exception as e:
            logging.warning(f"External analysis failed: {e}")
            external_analysis = "External analysis unavailable"

    # Enhanced prompt with external data
    prompt_template = """
You are a professional Forex trader AI with access to both technical and fundamental analysis.

TECHNICAL DATA:
- Current price: {price}
- Recent closing prices: {closes}
- Recent highs: {highs}
- Recent lows: {lows}
- Tick volumes: {volumes}

EXTERNAL MARKET ANALYSIS:
{external_analysis}

INSTRUCTIONS:
1. Analyze the technical indicators (price action, volume, trends)
2. Consider the external market sentiment and news
3. Make a trading decision based on combined analysis
4. Respond with EXACTLY ONE WORD: BUY, SELL, or HOLD

Decision:"""

    prompt = prompt_template.format(
        price=current_price,
        closes=closes[-5:],
        highs=highs[-5:],
        lows=lows[-5:],
        volumes=volumes[-5:],
        external_analysis=external_analysis,
    )

    response = llm.invoke(prompt).content.strip().upper()

    # Extract just the decision word
    decision_words = ["BUY", "SELL", "HOLD"]
    for word in decision_words:
        if word in response:
            decision = word
            break
    else:
        decision = "HOLD"  # Default to HOLD if unclear

    logging.info(f"🤖 AI Decision: {decision}")
    if external_analysis and external_analysis != "External analysis unavailable":
        logging.info(f"📊 Based on technical + external analysis")
    else:
        logging.info(f"📊 Based on technical analysis only")

    return decision


# === P/L % Calculation ===
def calculate_profit_loss():
    global last_trade
    if not last_trade["action"] or not last_trade["price"]:
        return "No open trade to track."

    current_tick = mt5.symbol_info_tick(SYMBOL)
    if not current_tick:
        return "Tick unavailable"

    current_price = (
        current_tick.bid if last_trade["action"] == "SELL" else current_tick.ask
    )
    entry_price = last_trade["price"]
    direction = 1 if last_trade["action"] == "BUY" else -1
    change_percent = ((current_price - entry_price) / entry_price) * 100 * direction

    return f"{change_percent:+.2f}% {'Profit' if change_percent > 0 else 'Loss'}"


# === Run the AI Loop ===
def run_forex_ai():
    init_mt5()
    llm = get_gemini_llm()

    while True:
        try:
            rates = get_forex_data()
            decision = generate_decision(llm, rates)

            if decision in ["BUY", "SELL"]:
                place_trade(decision)
            else:
                logging.info("Decision: HOLD — no trade placed.")

            pl_result = calculate_profit_loss()
            logging.info("P&L update: %s", pl_result)

        except Exception as e:
            logging.error("Error: %s", e)

        logging.info("Next check in %d seconds...\n", DELAY_SECONDS)
        time.sleep(DELAY_SECONDS)


# === Run the Bot ===
if __name__ == "__main__":
    run_forex_ai()
