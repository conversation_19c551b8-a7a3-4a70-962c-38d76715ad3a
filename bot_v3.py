# === Advanced Forex Trading Bot v3 with Comprehensive Trading Journal ===

import MetaTrader5 as mt5
import time
import os
import logging
import numpy as np
import json
import csv
from datetime import datetime, timezone
from dotenv import load_dotenv
from langchain_google_genai import ChatGoogleGenerativeAI

load_dotenv()

# === Load credentials ===
MT5_LOGIN = os.getenv("MT5_LOGIN")
MT5_PASSWORD = os.getenv("MT5_PASSWORD")
MT5_SERVER = os.getenv("MT5_SERVER")
GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY")

# === Constants ===
SYMBOL = "EURUSD"
LOT_SIZE = 0.1
TIMEFRAME = mt5.TIMEFRAME_H1  # 1-hour charts for better entry timing
ANALYSIS_BARS = 100  # Analyze last 100 hours
DELAY_SECONDS = 300  # Check every 5 minutes
MAGIC_ID = 345678

# Trade Management Settings
TAKE_PROFIT_PERCENT = 2.0
STOP_LOSS_PERCENT = 1.0
MAX_TRADE_DURATION = 14400  # 4 hours max
RISK_PERCENT = 1.0  # Risk 1% of account per trade

# Trading Journal Settings
JOURNAL_FILE = "trading_journal_v3.csv"
PERFORMANCE_FILE = "performance_dashboard_v3.json"

# Track current trade
current_trade = {
    "ticket": None,
    "entry_time": None,
    "entry_price": None,
    "action": None,
    "strategy": None,
    "market_conditions": None,
    "entry_trigger": None,
    "rationale": None,
    "stop_loss": None,
    "take_profit": None,
    "risk_reward_ratio": None,
    "position_size": None,
    "risk_percent": None,
}

# Performance tracking
performance_stats = {
    "total_trades": 0,
    "winning_trades": 0,
    "losing_trades": 0,
    "total_pips": 0,
    "total_profit": 0,
    "best_trade": 0,
    "worst_trade": 0,
    "avg_win": 0,
    "avg_loss": 0,
    "win_rate": 0,
    "avg_rr_ratio": 0,
    "strategies_performance": {},
    "monthly_performance": {},
}

# === Logger ===
logging.basicConfig(level=logging.INFO, format="[%(asctime)s] %(message)s")


# === Trading Journal Functions ===
def initialize_journal():
    """Initialize trading journal CSV file"""
    if not os.path.exists(JOURNAL_FILE):
        headers = [
            "Date_Time_Entry",
            "Date_Time_Exit",
            "Currency_Pair",
            "Direction",
            "Timeframe",
            "Entry_Price",
            "Exit_Price",
            "Stop_Loss",
            "Take_Profit",
            "RR_Ratio",
            "Position_Size",
            "Leverage",
            "Risk_Percent",
            "Market_Conditions",
            "Strategy_Used",
            "Entry_Trigger",
            "Trade_Rationale",
            "Pips_Gained_Lost",
            "Monetary_PL",
            "Percent_Change",
            "Setup_Valid",
            "Mistakes_Deviations",
            "Lessons_Learned",
            "Trade_Outcome",
            "Trade_Duration_Minutes",
        ]

        with open(JOURNAL_FILE, "w", newline="", encoding="utf-8") as file:
            writer = csv.writer(file)
            writer.writerow(headers)

        logging.info(f"📊 Trading journal initialized: {JOURNAL_FILE}")


def log_trade_entry(trade_data):
    """Log trade entry to journal"""
    global current_trade
    current_trade.update(trade_data)
    logging.info("📝 Trade entry logged to journal")


def log_trade_exit(exit_data):
    """Log complete trade to journal CSV"""
    try:
        # Calculate trade metrics
        entry_price = current_trade["entry_price"]
        exit_price = exit_data["exit_price"]
        direction = current_trade["action"]

        # Calculate pips and profit
        if direction == "BUY":
            pips = (exit_price - entry_price) * 10000
        else:
            pips = (entry_price - exit_price) * 10000

        monetary_pl = exit_data["profit"]
        percent_change = (monetary_pl / 100000) * 100  # Assuming $100k account

        # Calculate trade duration
        entry_time = datetime.fromisoformat(current_trade["entry_time"])
        exit_time = datetime.fromisoformat(exit_data["exit_time"])
        duration_minutes = (exit_time - entry_time).total_seconds() / 60

        # Prepare journal entry
        journal_entry = [
            current_trade["entry_time"],
            exit_data["exit_time"],
            SYMBOL,
            direction,
            "1H",
            entry_price,
            exit_price,
            current_trade["stop_loss"],
            current_trade["take_profit"],
            current_trade["risk_reward_ratio"],
            current_trade["position_size"],
            "1:100",  # Typical forex leverage
            current_trade["risk_percent"],
            current_trade["market_conditions"],
            current_trade["strategy"],
            current_trade["entry_trigger"],
            current_trade["rationale"],
            round(pips, 1),
            round(monetary_pl, 2),
            round(percent_change, 3),
            exit_data["setup_valid"],
            exit_data.get("mistakes", "None"),
            exit_data.get("lessons", "Trade executed as planned"),
            "Win" if monetary_pl > 0 else "Loss",
            round(duration_minutes, 1),
        ]

        # Write to CSV
        with open(JOURNAL_FILE, "a", newline="", encoding="utf-8") as file:
            writer = csv.writer(file)
            writer.writerow(journal_entry)

        # Update performance stats
        update_performance_stats(pips, monetary_pl, current_trade["strategy"])

        logging.info(f"📊 Complete trade logged: {pips:+.1f} pips, ${monetary_pl:+.2f}")

        # Reset current trade
        reset_current_trade()

    except Exception as e:
        logging.error(f"Failed to log trade exit: {e}")


def update_performance_stats(pips, profit, strategy):
    """Update performance statistics"""
    global performance_stats

    performance_stats["total_trades"] += 1
    performance_stats["total_pips"] += pips
    performance_stats["total_profit"] += profit

    if profit > 0:
        performance_stats["winning_trades"] += 1
        performance_stats["avg_win"] = (
            performance_stats["avg_win"] * (performance_stats["winning_trades"] - 1)
            + profit
        ) / performance_stats["winning_trades"]
        if profit > performance_stats["best_trade"]:
            performance_stats["best_trade"] = profit
    else:
        performance_stats["losing_trades"] += 1
        performance_stats["avg_loss"] = (
            performance_stats["avg_loss"] * (performance_stats["losing_trades"] - 1)
            + profit
        ) / performance_stats["losing_trades"]
        if profit < performance_stats["worst_trade"]:
            performance_stats["worst_trade"] = profit

    # Calculate win rate
    performance_stats["win_rate"] = (
        performance_stats["winning_trades"] / performance_stats["total_trades"]
    ) * 100

    # Track strategy performance
    if strategy not in performance_stats["strategies_performance"]:
        performance_stats["strategies_performance"][strategy] = {
            "trades": 0,
            "profit": 0,
            "wins": 0,
        }

    performance_stats["strategies_performance"][strategy]["trades"] += 1
    performance_stats["strategies_performance"][strategy]["profit"] += profit
    if profit > 0:
        performance_stats["strategies_performance"][strategy]["wins"] += 1

    # Save performance data
    save_performance_dashboard()


def save_performance_dashboard():
    """Save performance dashboard to JSON"""
    try:
        with open(PERFORMANCE_FILE, "w") as file:
            json.dump(performance_stats, file, indent=2)
    except Exception as e:
        logging.error(f"Failed to save performance dashboard: {e}")


def reset_current_trade():
    """Reset current trade tracking"""
    global current_trade
    current_trade = {
        "ticket": None,
        "entry_time": None,
        "entry_price": None,
        "action": None,
        "strategy": None,
        "market_conditions": None,
        "entry_trigger": None,
        "rationale": None,
        "stop_loss": None,
        "take_profit": None,
        "risk_reward_ratio": None,
        "position_size": None,
        "risk_percent": None,
    }


# === MT5 Connection ===
def init_mt5():
    if not mt5.initialize():
        logging.error("MT5 initialize failed: %s", mt5.last_error())
        exit()
    if not mt5.login(int(MT5_LOGIN), password=MT5_PASSWORD, server=MT5_SERVER):
        logging.error("Login failed: %s", mt5.last_error())
        exit()
    logging.info("Connected to MetaTrader5 for Advanced Trading with Journal")


# === Technical Analysis Functions ===
def get_market_data(symbol=SYMBOL, bars=ANALYSIS_BARS):
    """Get hourly OHLC data for analysis"""
    rates = mt5.copy_rates_from_pos(symbol, TIMEFRAME, 0, bars)
    if rates is None or len(rates) < 50:
        raise ValueError("Insufficient data for analysis")
    return rates


def calculate_technical_indicators(rates):
    """Calculate comprehensive technical indicators"""
    closes = np.array([r["close"] for r in rates])
    highs = np.array([r["high"] for r in rates])
    lows = np.array([r["low"] for r in rates])
    volumes = np.array([r["tick_volume"] for r in rates])

    # Moving Averages
    sma_20 = np.convolve(closes, np.ones(20) / 20, mode="valid")
    sma_50 = np.convolve(closes, np.ones(50) / 50, mode="valid")

    # EMA calculation
    def calculate_ema(prices, period):
        alpha = 2 / (period + 1)
        ema = [prices[0]]
        for price in prices[1:]:
            ema.append(alpha * price + (1 - alpha) * ema[-1])
        return np.array(ema)

    ema_12 = calculate_ema(closes, 12)
    ema_26 = calculate_ema(closes, 26)

    # RSI calculation
    def calculate_rsi(prices, period=14):
        deltas = np.diff(prices)
        gains = np.where(deltas > 0, deltas, 0)
        losses = np.where(deltas < 0, -deltas, 0)

        avg_gains = np.mean(gains[:period])
        avg_losses = np.mean(losses[:period])

        rs = avg_gains / avg_losses if avg_losses != 0 else 0
        return 100 - (100 / (1 + rs))

    rsi = calculate_rsi(closes)

    # MACD
    macd_line = ema_12[-1] - ema_26[-1] if len(ema_12) > 0 and len(ema_26) > 0 else 0

    # Bollinger Bands
    bb_period = 20
    bb_std = 2
    bb_sma = sma_20[-1] if len(sma_20) > 0 else closes[-1]
    bb_std_dev = np.std(closes[-bb_period:])
    bb_upper = bb_sma + (bb_std * bb_std_dev)
    bb_lower = bb_sma - (bb_std * bb_std_dev)

    return {
        "sma_20": sma_20[-1] if len(sma_20) > 0 else closes[-1],
        "sma_50": sma_50[-1] if len(sma_50) > 0 else closes[-1],
        "ema_12": ema_12[-1],
        "ema_26": ema_26[-1],
        "rsi": rsi,
        "macd": macd_line,
        "bb_upper": bb_upper,
        "bb_lower": bb_lower,
        "bb_middle": bb_sma,
        "current_price": closes[-1],
        "volume_avg": np.mean(volumes[-10:]),
        "volume_current": volumes[-1],
    }


# === Market Analysis & Strategy Functions ===
def analyze_market_conditions(indicators):
    """Analyze current market conditions"""
    current_price = indicators["current_price"]
    sma_20 = indicators["sma_20"]
    sma_50 = indicators["sma_50"]
    rsi = indicators["rsi"]
    bb_upper = indicators["bb_upper"]
    bb_lower = indicators["bb_lower"]

    conditions = []

    # Trend analysis
    if current_price > sma_20 > sma_50:
        trend = "Strong Uptrend"
        conditions.append("bullish_trend")
    elif current_price > sma_20 and sma_20 < sma_50:
        trend = "Weak Uptrend"
        conditions.append("weak_bullish")
    elif current_price < sma_20 < sma_50:
        trend = "Strong Downtrend"
        conditions.append("bearish_trend")
    elif current_price < sma_20 and sma_20 > sma_50:
        trend = "Weak Downtrend"
        conditions.append("weak_bearish")
    else:
        trend = "Sideways/Consolidation"
        conditions.append("sideways")

    # Volatility analysis
    bb_width = ((bb_upper - bb_lower) / current_price) * 100
    if bb_width > 1.5:
        volatility = "High"
        conditions.append("high_volatility")
    elif bb_width < 0.8:
        volatility = "Low"
        conditions.append("low_volatility")
    else:
        volatility = "Normal"
        conditions.append("normal_volatility")

    # RSI conditions
    if rsi > 70:
        rsi_condition = "Overbought"
        conditions.append("overbought")
    elif rsi < 30:
        rsi_condition = "Oversold"
        conditions.append("oversold")
    else:
        rsi_condition = "Neutral"
        conditions.append("rsi_neutral")

    return {
        "trend": trend,
        "volatility": volatility,
        "rsi_condition": rsi_condition,
        "conditions": conditions,
        "summary": f"{trend}, {volatility} volatility, RSI {rsi_condition}",
    }


def identify_trading_strategy(indicators, market_conditions):
    """Identify the best trading strategy based on market conditions"""
    conditions = market_conditions["conditions"]
    current_price = indicators["current_price"]
    sma_20 = indicators["sma_20"]
    rsi = indicators["rsi"]
    macd = indicators["macd"]
    bb_upper = indicators["bb_upper"]
    bb_lower = indicators["bb_lower"]

    strategies = []

    # Trend Following Strategy
    if "bullish_trend" in conditions and rsi < 70:
        strategies.append(
            {
                "name": "Trend Following - Bullish",
                "signal": "BUY",
                "confidence": 0.8,
                "entry_trigger": "Price above SMA20, uptrend confirmed",
                "rationale": "Strong uptrend with room for more upside (RSI < 70)",
            }
        )

    if "bearish_trend" in conditions and rsi > 30:
        strategies.append(
            {
                "name": "Trend Following - Bearish",
                "signal": "SELL",
                "confidence": 0.8,
                "entry_trigger": "Price below SMA20, downtrend confirmed",
                "rationale": "Strong downtrend with room for more downside (RSI > 30)",
            }
        )

    # Mean Reversion Strategy
    if current_price <= bb_lower and rsi < 30:
        strategies.append(
            {
                "name": "Mean Reversion - Oversold Bounce",
                "signal": "BUY",
                "confidence": 0.7,
                "entry_trigger": "Price at lower Bollinger Band + RSI oversold",
                "rationale": "Oversold conditions suggest potential bounce",
            }
        )

    if current_price >= bb_upper and rsi > 70:
        strategies.append(
            {
                "name": "Mean Reversion - Overbought Pullback",
                "signal": "SELL",
                "confidence": 0.7,
                "entry_trigger": "Price at upper Bollinger Band + RSI overbought",
                "rationale": "Overbought conditions suggest potential pullback",
            }
        )

    # Breakout Strategy
    if "low_volatility" in conditions and abs(macd) > 0.0001:
        if macd > 0 and current_price > sma_20:
            strategies.append(
                {
                    "name": "Breakout - Bullish",
                    "signal": "BUY",
                    "confidence": 0.6,
                    "entry_trigger": "MACD bullish + breakout from consolidation",
                    "rationale": "Low volatility breakout with momentum confirmation",
                }
            )
        elif macd < 0 and current_price < sma_20:
            strategies.append(
                {
                    "name": "Breakout - Bearish",
                    "signal": "SELL",
                    "confidence": 0.6,
                    "entry_trigger": "MACD bearish + breakdown from consolidation",
                    "rationale": "Low volatility breakdown with momentum confirmation",
                }
            )

    # Return best strategy
    if strategies:
        best_strategy = max(strategies, key=lambda x: x["confidence"])
        return best_strategy
    else:
        return {
            "name": "No Clear Strategy",
            "signal": "HOLD",
            "confidence": 0.0,
            "entry_trigger": "Mixed signals",
            "rationale": "Market conditions do not favor any clear strategy",
        }


# === Gemini AI Analysis ===
def get_gemini_llm():
    return ChatGoogleGenerativeAI(
        model="gemini-1.5-flash",
        temperature=0.1,
        google_api_key=GOOGLE_API_KEY,
    )


def get_ai_trade_decision(llm, indicators, market_conditions, strategy):
    """Get AI confirmation for trade decision"""

    prompt = f"""
You are a professional forex trader analyzing EURUSD. Review this comprehensive analysis and provide your trading decision.

TECHNICAL INDICATORS:
- Current Price: {indicators['current_price']:.5f}
- SMA20: {indicators['sma_20']:.5f}
- SMA50: {indicators['sma_50']:.5f}
- RSI: {indicators['rsi']:.1f}
- MACD: {indicators['macd']:.6f}
- Bollinger Bands: Upper {indicators['bb_upper']:.5f}, Lower {indicators['bb_lower']:.5f}

MARKET CONDITIONS:
{market_conditions['summary']}

IDENTIFIED STRATEGY:
Strategy: {strategy['name']}
Signal: {strategy['signal']}
Confidence: {strategy['confidence']:.1%}
Entry Trigger: {strategy['entry_trigger']}
Rationale: {strategy['rationale']}

RISK MANAGEMENT:
- Risk per trade: {RISK_PERCENT}%
- Take Profit target: {TAKE_PROFIT_PERCENT}%
- Stop Loss: {STOP_LOSS_PERCENT}%

Based on this analysis, do you confirm this trade setup?
Consider:
1. Technical confluence
2. Risk/reward ratio
3. Market timing
4. Overall probability of success

Respond with: BUY, SELL, or HOLD
Also provide a brief reason for your decision.
"""

    response = llm.invoke(prompt).content.strip()

    # Extract decision
    if "BUY" in response.upper():
        decision = "BUY"
    elif "SELL" in response.upper():
        decision = "SELL"
    else:
        decision = "HOLD"

    return decision, response


# === Trade Execution ===
def calculate_position_size(account_balance, risk_percent, stop_loss_pips):
    """Calculate position size based on risk management"""
    risk_amount = account_balance * (risk_percent / 100)
    pip_value = 1  # For EURUSD, 1 pip = $1 for 0.1 lot
    position_size = risk_amount / (stop_loss_pips * pip_value)
    return min(position_size, LOT_SIZE)  # Cap at maximum lot size


def place_trade_with_journal(action, strategy, market_conditions, indicators):
    """Execute trade and log to journal"""
    try:
        tick = mt5.symbol_info_tick(SYMBOL)
        if not tick:
            logging.error("Tick data unavailable")
            return None

        entry_price = tick.ask if action == "BUY" else tick.bid

        # Calculate stop loss and take profit
        if action == "BUY":
            stop_loss = entry_price * (1 - STOP_LOSS_PERCENT / 100)
            take_profit = entry_price * (1 + TAKE_PROFIT_PERCENT / 100)
        else:
            stop_loss = entry_price * (1 + STOP_LOSS_PERCENT / 100)
            take_profit = entry_price * (1 - TAKE_PROFIT_PERCENT / 100)

        # Calculate risk-reward ratio
        if action == "BUY":
            risk_pips = (entry_price - stop_loss) * 10000
            reward_pips = (take_profit - entry_price) * 10000
        else:
            risk_pips = (stop_loss - entry_price) * 10000
            reward_pips = (entry_price - take_profit) * 10000

        rr_ratio = reward_pips / risk_pips if risk_pips > 0 else 0

        # Calculate position size
        account_info = mt5.account_info()
        if account_info:
            position_size = calculate_position_size(
                account_info.balance, RISK_PERCENT, risk_pips
            )
        else:
            position_size = LOT_SIZE

        # Execute trade
        order_type = mt5.ORDER_TYPE_BUY if action == "BUY" else mt5.ORDER_TYPE_SELL

        request = {
            "action": mt5.TRADE_ACTION_DEAL,
            "symbol": SYMBOL,
            "volume": position_size,
            "type": order_type,
            "price": entry_price,
            "sl": stop_loss,
            "tp": take_profit,
            "deviation": 20,
            "magic": MAGIC_ID,
            "comment": f"Bot v3 - {strategy['name']}",
            "type_time": mt5.ORDER_TIME_GTC,
            "type_filling": mt5.ORDER_FILLING_FOK,
        }

        result = mt5.order_send(request)

        if result.retcode == mt5.TRADE_RETCODE_DONE:
            # Log trade entry to journal
            trade_data = {
                "ticket": result.order,
                "entry_time": datetime.now(timezone.utc).isoformat(),
                "entry_price": entry_price,
                "action": action,
                "strategy": strategy["name"],
                "market_conditions": market_conditions["summary"],
                "entry_trigger": strategy["entry_trigger"],
                "rationale": strategy["rationale"],
                "stop_loss": stop_loss,
                "take_profit": take_profit,
                "risk_reward_ratio": round(rr_ratio, 2),
                "position_size": position_size,
                "risk_percent": RISK_PERCENT,
            }

            log_trade_entry(trade_data)

            logging.info(f"✅ {action} trade executed: {entry_price:.5f}")
            logging.info(f"   Strategy: {strategy['name']}")
            logging.info(f"   SL: {stop_loss:.5f}, TP: {take_profit:.5f}")
            logging.info(f"   R:R Ratio: 1:{rr_ratio:.2f}")
            logging.info(f"   Position Size: {position_size:.2f} lots")

            return result
        else:
            logging.error(f"❌ Trade failed: {result.retcode}")
            return None

    except Exception as e:
        logging.error(f"Trade execution error: {e}")
        return None


# === Position Management ===
def get_open_positions(symbol=SYMBOL):
    """Get open positions for the symbol"""
    positions = mt5.positions_get(symbol=symbol)
    if positions is None:
        return []
    return [pos for pos in positions if pos.magic == MAGIC_ID]


def monitor_and_close_positions():
    """Monitor positions and close when conditions are met"""
    positions = get_open_positions()

    for position in positions:
        try:
            # Check if position should be closed
            current_time = time.time()
            entry_time = (
                datetime.fromisoformat(current_trade["entry_time"]).timestamp()
                if current_trade["entry_time"]
                else current_time
            )
            duration = current_time - entry_time

            should_close = False
            close_reason = ""
            setup_valid = "Yes"
            mistakes = "None"
            lessons = "Trade executed as planned"

            # Check time-based closure
            if duration >= MAX_TRADE_DURATION:
                should_close = True
                close_reason = "Max duration reached"
                lessons = "Consider shorter timeframes for faster exits"

            # Check if SL/TP was hit (position will be auto-closed by MT5)
            # This is just for logging purposes

            if should_close:
                close_position_with_journal(
                    position, close_reason, setup_valid, mistakes, lessons
                )

        except Exception as e:
            logging.error(f"Error monitoring position {position.ticket}: {e}")


def close_position_with_journal(position, reason, setup_valid, mistakes, lessons):
    """Close position and log to journal"""
    try:
        # Get current price for closing
        tick = mt5.symbol_info_tick(position.symbol)
        if not tick:
            logging.error("Cannot get tick data for position closure")
            return False

        # Determine close price
        if position.type == mt5.POSITION_TYPE_BUY:
            close_price = tick.bid
            trade_type = mt5.ORDER_TYPE_SELL
        else:
            close_price = tick.ask
            trade_type = mt5.ORDER_TYPE_BUY

        # Close position
        close_request = {
            "action": mt5.TRADE_ACTION_DEAL,
            "symbol": position.symbol,
            "volume": position.volume,
            "type": trade_type,
            "position": position.ticket,
            "price": close_price,
            "deviation": 20,
            "magic": MAGIC_ID,
            "comment": f"Bot v3 Close - {reason}",
            "type_time": mt5.ORDER_TIME_GTC,
            "type_filling": mt5.ORDER_FILLING_FOK,
        }

        result = mt5.order_send(close_request)

        if result.retcode == mt5.TRADE_RETCODE_DONE:
            # Log trade exit
            exit_data = {
                "exit_price": close_price,
                "exit_time": datetime.now(timezone.utc).isoformat(),
                "profit": position.profit,
                "setup_valid": setup_valid,
                "mistakes": mistakes,
                "lessons": lessons,
            }

            log_trade_exit(exit_data)

            logging.info(f"✅ Position closed: {reason}")
            logging.info(f"   Profit: ${position.profit:.2f}")
            logging.info(f"   Close Price: {close_price:.5f}")

            return True
        else:
            logging.error(f"❌ Failed to close position: {result.retcode}")
            return False

    except Exception as e:
        logging.error(f"Error closing position: {e}")
        return False


def display_performance_summary():
    """Display current performance statistics"""
    if performance_stats["total_trades"] > 0:
        logging.info("📊 PERFORMANCE SUMMARY:")
        logging.info(f"   Total Trades: {performance_stats['total_trades']}")
        logging.info(f"   Win Rate: {performance_stats['win_rate']:.1f}%")
        logging.info(f"   Total Profit: ${performance_stats['total_profit']:.2f}")
        logging.info(f"   Total Pips: {performance_stats['total_pips']:+.1f}")
        logging.info(f"   Best Trade: ${performance_stats['best_trade']:.2f}")
        logging.info(f"   Worst Trade: ${performance_stats['worst_trade']:.2f}")

        if performance_stats["strategies_performance"]:
            logging.info("   Strategy Performance:")
            for strategy, stats in performance_stats["strategies_performance"].items():
                win_rate = (
                    (stats["wins"] / stats["trades"]) * 100
                    if stats["trades"] > 0
                    else 0
                )
                logging.info(
                    f"     {strategy}: {stats['trades']} trades, {win_rate:.1f}% win rate, ${stats['profit']:.2f}"
                )


# === Main Trading Loop ===
def run_advanced_trading_bot():
    """Main trading loop with comprehensive analysis and journaling"""
    init_mt5()
    initialize_journal()
    llm = get_gemini_llm()

    logging.info("🚀 Advanced Trading Bot v3 Started")
    logging.info(f"📊 Analyzing {SYMBOL} with comprehensive journaling")
    logging.info(f"📝 Journal file: {JOURNAL_FILE}")
    logging.info(f"📈 Performance tracking: {PERFORMANCE_FILE}")

    cycle_count = 0

    while True:
        try:
            cycle_count += 1
            logging.info(f"\n🔄 Analysis Cycle #{cycle_count}")

            # Get market data and calculate indicators
            rates = get_market_data()
            indicators = calculate_technical_indicators(rates)

            # Analyze market conditions
            market_conditions = analyze_market_conditions(indicators)

            # Identify trading strategy
            strategy = identify_trading_strategy(indicators, market_conditions)

            # Log analysis results
            logging.info(f"📊 Market Analysis:")
            logging.info(f"   Price: {indicators['current_price']:.5f}")
            logging.info(f"   Conditions: {market_conditions['summary']}")
            logging.info(f"   Strategy: {strategy['name']}")
            logging.info(f"   Signal: {strategy['signal']}")
            logging.info(f"   Confidence: {strategy['confidence']:.1%}")

            # Check existing positions
            positions = get_open_positions()

            if positions:
                logging.info(f"📊 Managing {len(positions)} open position(s)")
                monitor_and_close_positions()

                # Display current P&L
                total_profit = sum(pos.profit for pos in get_open_positions())
                if total_profit != 0:
                    logging.info(f"💰 Current Open P&L: ${total_profit:.2f}")

            else:
                # Look for new trading opportunities
                if strategy["signal"] in ["BUY", "SELL"]:
                    # Get AI confirmation
                    ai_decision, ai_reasoning = get_ai_trade_decision(
                        llm, indicators, market_conditions, strategy
                    )

                    logging.info(f"🤖 AI Decision: {ai_decision}")
                    logging.info(f"   Reasoning: {ai_reasoning[:100]}...")

                    # Execute trade if AI confirms
                    if ai_decision == strategy["signal"]:
                        place_trade_with_journal(
                            ai_decision, strategy, market_conditions, indicators
                        )
                    else:
                        logging.info(
                            "❌ AI did not confirm strategy signal - no trade executed"
                        )
                else:
                    logging.info("⏸️  No clear trading opportunity - waiting")

            # Display performance summary every 10 cycles
            if cycle_count % 10 == 0:
                display_performance_summary()

        except Exception as e:
            logging.error(f"Error in trading loop: {e}")

        logging.info(f"⏰ Next analysis in {DELAY_SECONDS} seconds...")
        time.sleep(DELAY_SECONDS)


if __name__ == "__main__":
    print("🚀 Starting Advanced Trading Bot v3 with Trading Journal...")
    print("📊 Features: Technical Analysis + Comprehensive Trade Logging")
    print("📝 Journal: Automatic trade documentation and performance tracking")
    print("🎯 Strategies: Trend Following, Mean Reversion, Breakout")
    print("📈 Risk Management: 1% risk per trade, 2:1 R:R ratio")
    print("=" * 70)

    try:
        run_advanced_trading_bot()
    except KeyboardInterrupt:
        logging.info("Bot stopped by user")
        display_performance_summary()
        mt5.shutdown()
    except Exception as e:
        logging.error(f"Bot crashed: {e}")
        mt5.shutdown()
