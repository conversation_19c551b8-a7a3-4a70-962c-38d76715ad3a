# === Advanced Technical Analysis Forex Bot v2 - Pure Chart Analysis ===

import MetaTrader5 as mt5
import time
import os
import logging
import numpy as np
from dotenv import load_dotenv
from langchain_google_genai import ChatGoogleGenerativeAI

load_dotenv()

# === Load credentials ===
MT5_LOGIN = os.getenv("MT5_LOGIN")
MT5_PASSWORD = os.getenv("MT5_PASSWORD")
MT5_SERVER = os.getenv("MT5_SERVER")
GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY")

# === Constants ===
SYMBOL = "EURUSD"
LOT_SIZE = 0.1
TIMEFRAME = mt5.TIMEFRAME_D1  # Daily charts for trend analysis
ANALYSIS_BARS = 50  # Analyze last 50 days
DELAY_SECONDS = (
    300  # Check every 5 minutes (daily analysis doesn't need frequent checks)
)
MAGIC_ID = 234567

# Trade Management Settings
TAKE_PROFIT_PERCENT = 1.5
STOP_LOSS_PERCENT = 0.8
MAX_TRADE_DURATION = 86400  # 24 hours max

# Track trades
current_trade = {"action": None, "price": None, "time": None, "ticket": None}

# === Logger ===
logging.basicConfig(level=logging.INFO, format="[%(asctime)s] %(message)s")


# === MT5 Connection ===
def init_mt5():
    if not mt5.initialize():
        logging.error("MT5 initialize failed: %s", mt5.last_error())
        exit()
    if not mt5.login(int(MT5_LOGIN), password=MT5_PASSWORD, server=MT5_SERVER):
        logging.error("Login failed: %s", mt5.last_error())
        exit()
    logging.info("Connected to MetaTrader5 for Technical Analysis")


# === Technical Analysis Functions ===
def get_daily_data(symbol=SYMBOL, bars=ANALYSIS_BARS):
    """Get daily OHLC data for technical analysis"""
    rates = mt5.copy_rates_from_pos(symbol, TIMEFRAME, 0, bars)
    if rates is None or len(rates) < 20:
        raise ValueError("Insufficient daily data for analysis")
    return rates


def calculate_sma(prices, period):
    """Calculate Simple Moving Average"""
    return np.convolve(prices, np.ones(period) / period, mode="valid")


def calculate_ema(prices, period):
    """Calculate Exponential Moving Average"""
    alpha = 2 / (period + 1)
    ema = [prices[0]]
    for price in prices[1:]:
        ema.append(alpha * price + (1 - alpha) * ema[-1])
    return np.array(ema)


def find_support_resistance(highs, lows, closes, lookback=10):
    """Find key support and resistance levels"""
    support_levels = []
    resistance_levels = []

    for i in range(lookback, len(closes) - lookback):
        # Find local minima (support)
        if all(lows[i] <= lows[i - j] for j in range(1, lookback + 1)) and all(
            lows[i] <= lows[i + j] for j in range(1, lookback + 1)
        ):
            support_levels.append(lows[i])

        # Find local maxima (resistance)
        if all(highs[i] >= highs[i - j] for j in range(1, lookback + 1)) and all(
            highs[i] >= highs[i + j] for j in range(1, lookback + 1)
        ):
            resistance_levels.append(highs[i])

    return support_levels, resistance_levels


def detect_trend_direction(closes, period=20):
    """Detect overall trend direction using multiple timeframes"""
    if len(closes) < period:
        return "SIDEWAYS"

    # Short-term trend (last 10 days)
    short_trend = "UP" if closes[-1] > closes[-10] else "DOWN"

    # Medium-term trend (last 20 days)
    medium_trend = "UP" if closes[-1] > closes[-20] else "DOWN"

    # Long-term trend (using SMA)
    sma_20 = calculate_sma(closes, 20)
    long_trend = "UP" if closes[-1] > sma_20[-1] else "DOWN"

    # Consensus
    trends = [short_trend, medium_trend, long_trend]
    up_count = trends.count("UP")

    if up_count >= 2:
        return "UPTREND"
    elif up_count <= 1:
        return "DOWNTREND"
    else:
        return "SIDEWAYS"


def calculate_rsi(closes, period=14):
    """Calculate Relative Strength Index"""
    deltas = np.diff(closes)
    gains = np.where(deltas > 0, deltas, 0)
    losses = np.where(deltas < 0, -deltas, 0)

    avg_gains = np.mean(gains[:period])
    avg_losses = np.mean(losses[:period])

    rs = avg_gains / avg_losses if avg_losses != 0 else 0
    rsi = 100 - (100 / (1 + rs))

    return rsi


def analyze_candlestick_patterns(opens, highs, lows, closes):
    """Analyze recent candlestick patterns"""
    patterns = []

    # Get last 3 candles
    for i in range(-3, 0):
        o, h, l, c = opens[i], highs[i], lows[i], closes[i]
        body = abs(c - o)
        upper_shadow = h - max(o, c)
        lower_shadow = min(o, c) - l

        # Doji pattern
        if body < (h - l) * 0.1:
            patterns.append("DOJI")

        # Hammer/Hanging Man
        elif lower_shadow > body * 2 and upper_shadow < body * 0.5:
            if c > o:
                patterns.append("HAMMER_BULLISH")
            else:
                patterns.append("HANGING_MAN_BEARISH")

        # Shooting Star
        elif upper_shadow > body * 2 and lower_shadow < body * 0.5:
            patterns.append("SHOOTING_STAR_BEARISH")

        # Strong bullish/bearish candles
        elif body > (h - l) * 0.7:
            if c > o:
                patterns.append("STRONG_BULLISH")
            else:
                patterns.append("STRONG_BEARISH")

    return patterns


def perform_technical_analysis(rates):
    """Comprehensive technical analysis"""
    opens = np.array([r["open"] for r in rates])
    highs = np.array([r["high"] for r in rates])
    lows = np.array([r["low"] for r in rates])
    closes = np.array([r["close"] for r in rates])
    volumes = np.array([r["tick_volume"] for r in rates])

    current_price = closes[-1]

    # Technical indicators
    sma_20 = calculate_sma(closes, 20)
    sma_50 = calculate_sma(closes, 50)
    ema_12 = calculate_ema(closes, 12)
    ema_26 = calculate_ema(closes, 26)

    # Trend analysis
    trend = detect_trend_direction(closes)

    # Support/Resistance
    support_levels, resistance_levels = find_support_resistance(highs, lows, closes)

    # RSI
    rsi = calculate_rsi(closes)

    # Candlestick patterns
    patterns = analyze_candlestick_patterns(opens, highs, lows, closes)

    # MACD-like signal
    macd_line = ema_12[-1] - ema_26[-1] if len(ema_12) > 0 and len(ema_26) > 0 else 0

    # Volume analysis
    avg_volume = np.mean(volumes[-10:])
    current_volume = volumes[-1]
    volume_trend = "HIGH" if current_volume > avg_volume * 1.2 else "NORMAL"

    analysis = {
        "current_price": current_price,
        "trend": trend,
        "sma_20": sma_20[-1] if len(sma_20) > 0 else current_price,
        "sma_50": sma_50[-1] if len(sma_50) > 0 else current_price,
        "rsi": rsi,
        "macd_signal": macd_line,
        "support_levels": support_levels[-3:] if support_levels else [],
        "resistance_levels": resistance_levels[-3:] if resistance_levels else [],
        "candlestick_patterns": patterns,
        "volume_trend": volume_trend,
        "price_vs_sma20": (
            "ABOVE"
            if current_price > sma_20[-1]
            else "BELOW" if len(sma_20) > 0 else "NEUTRAL"
        ),
        "price_vs_sma50": (
            "ABOVE"
            if current_price > sma_50[-1]
            else "BELOW" if len(sma_50) > 0 else "NEUTRAL"
        ),
    }

    return analysis


# === Gemini LLM ===
def get_gemini_llm():
    return ChatGoogleGenerativeAI(
        model="gemini-1.5-flash",
        temperature=0.1,
        google_api_key=GOOGLE_API_KEY,
    )


# === AI Trading Decision ===
def generate_trading_decision(llm, analysis):
    """Generate trading decision based on technical analysis"""

    prompt = f"""
You are a professional technical analyst and forex trader. Analyze the following technical data for {SYMBOL} and make a trading decision.

TECHNICAL ANALYSIS DATA:
- Current Price: {analysis['current_price']:.5f}
- Overall Trend: {analysis['trend']}
- Price vs SMA20: {analysis['price_vs_sma20']} (SMA20: {analysis['sma_20']:.5f})
- Price vs SMA50: {analysis['price_vs_sma50']} (SMA50: {analysis['sma_50']:.5f})
- RSI: {analysis['rsi']:.2f}
- MACD Signal: {analysis['macd_signal']:.6f}
- Volume Trend: {analysis['volume_trend']}
- Recent Candlestick Patterns: {', '.join(analysis['candlestick_patterns']) if analysis['candlestick_patterns'] else 'None'}
- Support Levels: {[f'{s:.5f}' for s in analysis['support_levels']]}
- Resistance Levels: {[f'{r:.5f}' for r in analysis['resistance_levels']]}

TRADING RULES:
1. BUY signals: Uptrend + Price above SMA20 + RSI < 70 + Bullish patterns
2. SELL signals: Downtrend + Price below SMA20 + RSI > 30 + Bearish patterns  
3. HOLD: Mixed signals, sideways trend, or extreme RSI levels

DECISION CRITERIA:
- Consider trend direction (most important)
- Check if price is near support/resistance
- Evaluate RSI for overbought/oversold conditions
- Factor in candlestick patterns
- Assess volume confirmation

Based on this technical analysis, what is your trading decision?
Respond with exactly ONE WORD: BUY, SELL, or HOLD
"""

    response = llm.invoke(prompt).content.strip().upper()

    # Extract decision
    if "BUY" in response:
        decision = "BUY"
    elif "SELL" in response:
        decision = "SELL"
    else:
        decision = "HOLD"

    return decision


# === Trade Execution ===
def place_trade(action, symbol=SYMBOL, lot=LOT_SIZE):
    """Execute trade based on technical analysis"""
    global current_trade

    tick = mt5.symbol_info_tick(symbol)
    if not tick:
        logging.error("Tick data unavailable for trade execution")
        return None

    price = tick.ask if action == "BUY" else tick.bid
    order_type = mt5.ORDER_TYPE_BUY if action == "BUY" else mt5.ORDER_TYPE_SELL

    request = {
        "action": mt5.TRADE_ACTION_DEAL,
        "symbol": symbol,
        "volume": lot,
        "type": order_type,
        "price": price,
        "deviation": 20,
        "magic": MAGIC_ID,
        "comment": "Technical Analysis v2",
        "type_time": mt5.ORDER_TIME_GTC,
        "type_filling": mt5.ORDER_FILLING_FOK,
    }

    result = mt5.order_send(request)
    if result.retcode == mt5.TRADE_RETCODE_DONE:
        current_trade = {
            "action": action,
            "price": price,
            "time": time.time(),
            "ticket": result.order,
        }
        logging.info(f"✅ {action} trade executed at {price:.5f}")
        logging.info(f"   Trade Ticket: {result.order}")
        return result
    else:
        logging.error(f"❌ Trade failed: {result.retcode}")
        return None


# === Position Management ===
def get_open_positions(symbol=SYMBOL):
    """Get open positions for the symbol"""
    positions = mt5.positions_get(symbol=symbol)
    if positions is None:
        return []
    return [pos for pos in positions if pos.magic == MAGIC_ID]


def close_position(position):
    """Close a position"""
    try:
        if position.type == mt5.POSITION_TYPE_BUY:
            trade_type = mt5.ORDER_TYPE_SELL
            price = mt5.symbol_info_tick(position.symbol).bid
        else:
            trade_type = mt5.ORDER_TYPE_BUY
            price = mt5.symbol_info_tick(position.symbol).ask

        close_request = {
            "action": mt5.TRADE_ACTION_DEAL,
            "symbol": position.symbol,
            "volume": position.volume,
            "type": trade_type,
            "position": position.ticket,
            "price": price,
            "deviation": 20,
            "magic": MAGIC_ID,
            "comment": "Technical Analysis Close",
            "type_time": mt5.ORDER_TIME_GTC,
            "type_filling": mt5.ORDER_FILLING_FOK,
        }

        result = mt5.order_send(close_request)
        if result.retcode == mt5.TRADE_RETCODE_DONE:
            logging.info(f"✅ Position closed: Profit ${position.profit:.2f}")
            global current_trade
            current_trade = {
                "action": None,
                "price": None,
                "time": None,
                "ticket": None,
            }
            return True
        else:
            logging.error(f"❌ Failed to close position: {result.retcode}")
            return False
    except Exception as e:
        logging.error(f"Error closing position: {e}")
        return False


def check_trade_management():
    """Check if trades should be closed"""
    positions = get_open_positions()

    for position in positions:
        # Calculate P&L percentage
        current_tick = mt5.symbol_info_tick(position.symbol)
        if not current_tick:
            continue

        if position.type == mt5.POSITION_TYPE_BUY:
            current_price = current_tick.bid
        else:
            current_price = current_tick.ask

        entry_price = position.price_open
        direction = 1 if position.type == mt5.POSITION_TYPE_BUY else -1
        profit_percent = ((current_price - entry_price) / entry_price) * 100 * direction

        # Check closure conditions
        should_close = False
        reason = ""

        if profit_percent >= TAKE_PROFIT_PERCENT:
            should_close = True
            reason = f"Take Profit: +{profit_percent:.2f}%"
        elif profit_percent <= -STOP_LOSS_PERCENT:
            should_close = True
            reason = f"Stop Loss: {profit_percent:.2f}%"
        elif (
            current_trade["time"]
            and (time.time() - current_trade["time"]) >= MAX_TRADE_DURATION
        ):
            should_close = True
            reason = "Max duration reached"

        if should_close:
            logging.info(f"🔄 Closing trade: {reason}")
            close_position(position)


# === Main Trading Loop ===
def run_technical_analysis_bot():
    """Main trading loop with technical analysis"""
    init_mt5()
    llm = get_gemini_llm()

    logging.info("🚀 Technical Analysis Bot v2 Started")
    logging.info(f"📊 Analyzing {SYMBOL} on daily timeframe")
    logging.info(f"🎯 Strategy: Pure technical analysis")

    while True:
        try:
            # Get daily data for analysis
            rates = get_daily_data()
            logging.info(f"📈 Analyzing {len(rates)} days of {SYMBOL} data")

            # Perform technical analysis
            analysis = perform_technical_analysis(rates)

            # Log key technical levels
            logging.info(f"📊 Technical Analysis Summary:")
            logging.info(f"   Current Price: {analysis['current_price']:.5f}")
            logging.info(f"   Trend: {analysis['trend']}")
            logging.info(f"   RSI: {analysis['rsi']:.1f}")
            logging.info(f"   Price vs SMA20: {analysis['price_vs_sma20']}")
            logging.info(
                f"   Patterns: {', '.join(analysis['candlestick_patterns']) if analysis['candlestick_patterns'] else 'None'}"
            )

            # Check existing positions
            positions = get_open_positions()

            if positions:
                logging.info(f"📊 Managing {len(positions)} open position(s)")
                check_trade_management()

                # Calculate current P&L
                total_profit = sum(pos.profit for pos in positions)
                logging.info(f"💰 Current P&L: ${total_profit:.2f}")

            else:
                # Look for new trading opportunities
                logging.info("🔍 Analyzing for new trading opportunities")
                decision = generate_trading_decision(llm, analysis)

                logging.info(f"🤖 Technical Analysis Decision: {decision}")

                if decision in ["BUY", "SELL"]:
                    place_trade(decision)
                else:
                    logging.info("Decision: HOLD — waiting for better setup")

        except Exception as e:
            logging.error(f"Error in trading loop: {e}")

        logging.info(f"Next analysis in {DELAY_SECONDS} seconds...\n")
        time.sleep(DELAY_SECONDS)


if __name__ == "__main__":
    print("🚀 Starting Technical Analysis Forex Bot v2...")
    print("📊 Focus: Daily chart analysis, trend lines, technical patterns")
    print("🎯 Strategy: Pure technical analysis without external data")
    print("=" * 60)

    try:
        run_technical_analysis_bot()
    except KeyboardInterrupt:
        logging.info("Bot stopped by user")
        mt5.shutdown()
    except Exception as e:
        logging.error(f"Bot crashed: {e}")
        mt5.shutdown()
