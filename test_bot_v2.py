#!/usr/bin/env python3
"""
Test script for Technical Analysis Bot v2
Tests all technical analysis functions and chart analysis capabilities
"""

import sys
import numpy as np
from dotenv import load_dotenv
import MetaTrader5 as mt5

# Add current directory to path
sys.path.append('.')
from bot_v2 import (
    init_mt5,
    get_daily_data,
    perform_technical_analysis,
    generate_trading_decision,
    get_gemini_llm,
    calculate_sma,
    calculate_ema,
    detect_trend_direction,
    find_support_resistance,
    calculate_rsi,
    analyze_candlestick_patterns
)

load_dotenv()

def test_technical_analysis():
    """Test all technical analysis functions"""
    print("🔧 Testing Technical Analysis Bot v2")
    print("=" * 60)
    
    # Test MT5 connection
    try:
        init_mt5()
        print("✅ MT5 Connection: SUCCESS")
    except Exception as e:
        print(f"❌ MT5 Connection: FAILED - {e}")
        return
    
    # Test daily data retrieval
    try:
        rates = get_daily_data()
        print(f"✅ Daily Data Retrieval: SUCCESS - {len(rates)} days")
        print(f"   Date Range: {rates[0]['time']} to {rates[-1]['time']}")
        print(f"   Current Price: {rates[-1]['close']:.5f}")
    except Exception as e:
        print(f"❌ Daily Data Retrieval: FAILED - {e}")
        return
    
    # Test individual technical indicators
    print(f"\n📊 Testing Technical Indicators:")
    
    try:
        closes = np.array([r['close'] for r in rates])
        highs = np.array([r['high'] for r in rates])
        lows = np.array([r['low'] for r in rates])
        opens = np.array([r['open'] for r in rates])
        
        # Test SMA
        sma_20 = calculate_sma(closes, 20)
        print(f"✅ SMA(20): {sma_20[-1]:.5f}")
        
        # Test EMA
        ema_12 = calculate_ema(closes, 12)
        print(f"✅ EMA(12): {ema_12[-1]:.5f}")
        
        # Test RSI
        rsi = calculate_rsi(closes)
        print(f"✅ RSI(14): {rsi:.1f}")
        
        # Test trend detection
        trend = detect_trend_direction(closes)
        print(f"✅ Trend Direction: {trend}")
        
        # Test support/resistance
        support, resistance = find_support_resistance(highs, lows, closes)
        print(f"✅ Support Levels: {len(support)} found")
        print(f"✅ Resistance Levels: {len(resistance)} found")
        
        # Test candlestick patterns
        patterns = analyze_candlestick_patterns(opens, highs, lows, closes)
        print(f"✅ Candlestick Patterns: {', '.join(patterns) if patterns else 'None detected'}")
        
    except Exception as e:
        print(f"❌ Technical Indicators: FAILED - {e}")
        return
    
    # Test comprehensive technical analysis
    print(f"\n🔍 Testing Comprehensive Analysis:")
    
    try:
        analysis = perform_technical_analysis(rates)
        print(f"✅ Technical Analysis: SUCCESS")
        print(f"   Current Price: {analysis['current_price']:.5f}")
        print(f"   Trend: {analysis['trend']}")
        print(f"   RSI: {analysis['rsi']:.1f}")
        print(f"   Price vs SMA20: {analysis['price_vs_sma20']}")
        print(f"   Price vs SMA50: {analysis['price_vs_sma50']}")
        print(f"   MACD Signal: {analysis['macd_signal']:.6f}")
        print(f"   Volume Trend: {analysis['volume_trend']}")
        print(f"   Support Levels: {len(analysis['support_levels'])}")
        print(f"   Resistance Levels: {len(analysis['resistance_levels'])}")
        print(f"   Patterns: {', '.join(analysis['candlestick_patterns']) if analysis['candlestick_patterns'] else 'None'}")
        
    except Exception as e:
        print(f"❌ Comprehensive Analysis: FAILED - {e}")
        return
    
    # Test AI decision making
    print(f"\n🤖 Testing AI Trading Decision:")
    
    try:
        llm = get_gemini_llm()
        decision = generate_trading_decision(llm, analysis)
        print(f"✅ AI Decision Generation: SUCCESS")
        print(f"   Decision: {decision}")
        print(f"   Based on: {analysis['trend']} trend, RSI {analysis['rsi']:.1f}")
        
    except Exception as e:
        print(f"❌ AI Decision Generation: FAILED - {e}")
    
    # Display detailed analysis summary
    print(f"\n📈 DETAILED TECHNICAL ANALYSIS SUMMARY:")
    print(f"=" * 60)
    print(f"Symbol: EURUSD (Daily Chart)")
    print(f"Analysis Period: {len(rates)} days")
    print(f"Current Price: {analysis['current_price']:.5f}")
    print(f"")
    print(f"TREND ANALYSIS:")
    print(f"  Overall Trend: {analysis['trend']}")
    print(f"  Price vs SMA20: {analysis['price_vs_sma20']} ({analysis['sma_20']:.5f})")
    print(f"  Price vs SMA50: {analysis['price_vs_sma50']} ({analysis['sma_50']:.5f})")
    print(f"")
    print(f"MOMENTUM INDICATORS:")
    print(f"  RSI(14): {analysis['rsi']:.1f} {'(Overbought)' if analysis['rsi'] > 70 else '(Oversold)' if analysis['rsi'] < 30 else '(Neutral)'}")
    print(f"  MACD Signal: {analysis['macd_signal']:.6f}")
    print(f"")
    print(f"SUPPORT & RESISTANCE:")
    if analysis['support_levels']:
        print(f"  Support: {', '.join([f'{s:.5f}' for s in analysis['support_levels'][-3:]])}")
    else:
        print(f"  Support: No clear levels identified")
    
    if analysis['resistance_levels']:
        print(f"  Resistance: {', '.join([f'{r:.5f}' for r in analysis['resistance_levels'][-3:]])}")
    else:
        print(f"  Resistance: No clear levels identified")
    print(f"")
    print(f"PATTERN ANALYSIS:")
    print(f"  Recent Patterns: {', '.join(analysis['candlestick_patterns']) if analysis['candlestick_patterns'] else 'No significant patterns'}")
    print(f"  Volume Trend: {analysis['volume_trend']}")
    print(f"")
    print(f"TRADING SIGNAL:")
    print(f"  AI Decision: {decision}")
    print(f"  Confidence: Based on {analysis['trend']} trend and technical confluence")
    
    # Cleanup
    mt5.shutdown()
    print(f"\n🎉 Technical Analysis Testing Complete!")
    print("=" * 60)

def demo_strategy_explanation():
    """Explain the technical analysis strategy"""
    print(f"\n📚 TECHNICAL ANALYSIS STRATEGY EXPLANATION:")
    print("=" * 60)
    print("🎯 STRATEGY: Pure Technical Analysis")
    print("📊 TIMEFRAME: Daily Charts (D1)")
    print("📈 DATA PERIOD: 50 days historical analysis")
    print("")
    print("🔍 TECHNICAL INDICATORS USED:")
    print("  • Simple Moving Averages (SMA 20, SMA 50)")
    print("  • Exponential Moving Averages (EMA 12, EMA 26)")
    print("  • Relative Strength Index (RSI 14)")
    print("  • MACD-style momentum indicator")
    print("  • Support and Resistance levels")
    print("  • Candlestick pattern recognition")
    print("  • Volume trend analysis")
    print("")
    print("📋 TRADING RULES:")
    print("  BUY SIGNALS:")
    print("    ✓ Uptrend confirmed")
    print("    ✓ Price above SMA20")
    print("    ✓ RSI < 70 (not overbought)")
    print("    ✓ Bullish candlestick patterns")
    print("    ✓ Volume confirmation")
    print("")
    print("  SELL SIGNALS:")
    print("    ✓ Downtrend confirmed")
    print("    ✓ Price below SMA20")
    print("    ✓ RSI > 30 (not oversold)")
    print("    ✓ Bearish candlestick patterns")
    print("    ✓ Volume confirmation")
    print("")
    print("  HOLD CONDITIONS:")
    print("    ✓ Sideways/unclear trend")
    print("    ✓ Mixed technical signals")
    print("    ✓ Extreme RSI levels")
    print("    ✓ Price near major S/R levels")
    print("")
    print("🛡️ RISK MANAGEMENT:")
    print("  • Take Profit: 1.5%")
    print("  • Stop Loss: 0.8%")
    print("  • Max Trade Duration: 24 hours")
    print("  • Position Size: 0.1 lots")
    print("")
    print("⏰ ANALYSIS FREQUENCY:")
    print("  • Chart Analysis: Every 5 minutes")
    print("  • Decision Making: Based on daily patterns")
    print("  • Trade Management: Continuous monitoring")

if __name__ == "__main__":
    test_technical_analysis()
    demo_strategy_explanation()
