#!/usr/bin/env python3
"""
Test script for Bot v3 Trading Journal functionality
Tests journal creation, logging, and performance tracking
"""

import sys
import os
import csv
import json
from datetime import datetime, timezone

# Add current directory to path
sys.path.append('.')
from bot_v3 import (
    initialize_journal,
    log_trade_entry,
    log_trade_exit,
    update_performance_stats,
    save_performance_dashboard,
    display_performance_summary,
    JOURNAL_FILE,
    PERFORMANCE_FILE,
    performance_stats
)

def test_journal_functionality():
    """Test the trading journal system"""
    print("🔧 Testing Trading Journal v3 Functionality")
    print("=" * 60)
    
    # Test journal initialization
    try:
        initialize_journal()
        print("✅ Journal Initialization: SUCCESS")
        print(f"   Created: {JOURNAL_FILE}")
        
        # Check if file exists and has headers
        if os.path.exists(JOURNAL_FILE):
            with open(JOURNAL_FILE, 'r') as file:
                reader = csv.reader(file)
                headers = next(reader)
                print(f"   Headers: {len(headers)} columns")
                print(f"   Sample headers: {', '.join(headers[:5])}...")
        
    except Exception as e:
        print(f"❌ Journal Initialization: FAILED - {e}")
        return
    
    # Test trade entry logging
    print(f"\n📝 Testing Trade Entry Logging:")
    try:
        sample_trade_data = {
            "ticket": 12345,
            "entry_time": datetime.now(timezone.utc).isoformat(),
            "entry_price": 1.1050,
            "action": "BUY",
            "strategy": "Trend Following - Bullish",
            "market_conditions": "Strong Uptrend, Normal volatility, RSI Neutral",
            "entry_trigger": "Price above SMA20, uptrend confirmed",
            "rationale": "Strong uptrend with room for more upside (RSI < 70)",
            "stop_loss": 1.1039,
            "take_profit": 1.1072,
            "risk_reward_ratio": 2.0,
            "position_size": 0.1,
            "risk_percent": 1.0
        }
        
        log_trade_entry(sample_trade_data)
        print("✅ Trade Entry Logging: SUCCESS")
        print(f"   Strategy: {sample_trade_data['strategy']}")
        print(f"   Entry Price: {sample_trade_data['entry_price']}")
        print(f"   R:R Ratio: 1:{sample_trade_data['risk_reward_ratio']}")
        
    except Exception as e:
        print(f"❌ Trade Entry Logging: FAILED - {e}")
        return
    
    # Test trade exit logging
    print(f"\n📊 Testing Trade Exit Logging:")
    try:
        sample_exit_data = {
            "exit_price": 1.1072,
            "exit_time": datetime.now(timezone.utc).isoformat(),
            "profit": 22.0,
            "setup_valid": "Yes",
            "mistakes": "None",
            "lessons": "Trade executed as planned"
        }
        
        log_trade_exit(sample_exit_data)
        print("✅ Trade Exit Logging: SUCCESS")
        print(f"   Exit Price: {sample_exit_data['exit_price']}")
        print(f"   Profit: ${sample_exit_data['profit']}")
        print(f"   Setup Valid: {sample_exit_data['setup_valid']}")
        
    except Exception as e:
        print(f"❌ Trade Exit Logging: FAILED - {e}")
        return
    
    # Test performance tracking
    print(f"\n📈 Testing Performance Tracking:")
    try:
        # Add a few more sample trades
        update_performance_stats(22, 22.0, "Trend Following - Bullish")
        update_performance_stats(-15, -15.0, "Mean Reversion - Oversold Bounce")
        update_performance_stats(30, 30.0, "Breakout - Bullish")
        
        print("✅ Performance Tracking: SUCCESS")
        print(f"   Total Trades: {performance_stats['total_trades']}")
        print(f"   Win Rate: {performance_stats['win_rate']:.1f}%")
        print(f"   Total Profit: ${performance_stats['total_profit']:.2f}")
        
    except Exception as e:
        print(f"❌ Performance Tracking: FAILED - {e}")
        return
    
    # Test performance dashboard
    print(f"\n💾 Testing Performance Dashboard:")
    try:
        save_performance_dashboard()
        print("✅ Performance Dashboard: SUCCESS")
        print(f"   Saved: {PERFORMANCE_FILE}")
        
        # Check if file exists and is valid JSON
        if os.path.exists(PERFORMANCE_FILE):
            with open(PERFORMANCE_FILE, 'r') as file:
                data = json.load(file)
                print(f"   Data keys: {list(data.keys())}")
                print(f"   Strategies tracked: {len(data.get('strategies_performance', {}))}")
        
    except Exception as e:
        print(f"❌ Performance Dashboard: FAILED - {e}")
        return
    
    # Test performance summary display
    print(f"\n📊 Testing Performance Summary:")
    try:
        display_performance_summary()
        print("✅ Performance Summary: SUCCESS")
        
    except Exception as e:
        print(f"❌ Performance Summary: FAILED - {e}")
    
    # Display journal file contents
    print(f"\n📄 Journal File Contents:")
    try:
        if os.path.exists(JOURNAL_FILE):
            with open(JOURNAL_FILE, 'r') as file:
                lines = file.readlines()
                print(f"   Total lines: {len(lines)}")
                print(f"   Headers: {lines[0].strip()}")
                if len(lines) > 1:
                    print(f"   Sample entry: {lines[1][:100]}...")
        
    except Exception as e:
        print(f"Error reading journal file: {e}")
    
    print(f"\n🎉 Trading Journal Testing Complete!")
    print("=" * 60)
    
    print(f"\n📋 JOURNAL FEATURES VERIFIED:")
    print("   ✅ CSV file creation and management")
    print("   ✅ Trade entry logging with 25+ data points")
    print("   ✅ Trade exit logging with outcomes")
    print("   ✅ Performance statistics tracking")
    print("   ✅ Strategy-specific performance")
    print("   ✅ JSON dashboard export")
    print("   ✅ Real-time summary display")
    
    print(f"\n📊 FILES CREATED:")
    print(f"   📄 {JOURNAL_FILE} - Complete trade history")
    print(f"   📊 {PERFORMANCE_FILE} - Performance dashboard")
    
    print(f"\n🎯 READY FOR LIVE TRADING!")
    print("   The journal system is fully functional and ready to track")
    print("   all trades with comprehensive documentation.")

def demo_journal_structure():
    """Show the structure of the trading journal"""
    print(f"\n📋 TRADING JOURNAL STRUCTURE:")
    print("=" * 50)
    
    headers = [
        "Date_Time_Entry", "Date_Time_Exit", "Currency_Pair", "Direction", 
        "Timeframe", "Entry_Price", "Exit_Price", "Stop_Loss", "Take_Profit",
        "RR_Ratio", "Position_Size", "Leverage", "Risk_Percent", "Market_Conditions",
        "Strategy_Used", "Entry_Trigger", "Trade_Rationale", "Pips_Gained_Lost",
        "Monetary_PL", "Percent_Change", "Setup_Valid", "Mistakes_Deviations",
        "Lessons_Learned", "Trade_Outcome", "Trade_Duration_Minutes"
    ]
    
    for i, header in enumerate(headers, 1):
        print(f"   {i:2d}. {header}")
    
    print(f"\n📊 PERFORMANCE DASHBOARD STRUCTURE:")
    print("=" * 45)
    
    dashboard_keys = [
        "total_trades", "winning_trades", "losing_trades", "total_pips",
        "total_profit", "best_trade", "worst_trade", "avg_win", "avg_loss",
        "win_rate", "avg_rr_ratio", "strategies_performance", "monthly_performance"
    ]
    
    for i, key in enumerate(dashboard_keys, 1):
        print(f"   {i:2d}. {key}")

if __name__ == "__main__":
    test_journal_functionality()
    demo_journal_structure()
